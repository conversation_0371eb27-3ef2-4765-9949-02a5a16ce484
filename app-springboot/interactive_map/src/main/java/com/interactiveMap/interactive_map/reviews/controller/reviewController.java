package com.interactiveMap.interactive_map.reviews.controller;

import com.interactiveMap.interactive_map.reviews.dto.ReviewDTO;
import com.interactiveMap.interactive_map.reviews.service.ReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/review")
public class reviewController {

    @Autowired
    private ReviewService reviewService;

    @GetMapping("")
    public List<ReviewDTO> getAllReviews(){
        return reviewService.getAllReviews();
    }

    @GetMapping("/{id}")
    public ReviewDTO getReview(@PathVariable long id) {
        return reviewService.getReview(id);
    }

    @PostMapping("")
    public void addReview(@RequestBody ReviewDTO reviewDTO) {
        reviewService.addReview(reviewDTO);
    }

}