package com.interactiveMap.interactive_map.reviews.entity;


import jakarta.persistence.*;


@Entity
public class ReviewEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String reviewText;

    @Column(nullable = false)
    private int rating;

    @Column(nullable = false)
    private date date;

    public ReviewEntity() {
    }

    public ReviewEntity(String reviewText, int rating) {
    this.reviewText = reviewText;
    this.rating = rating;
    }

    public Long getId() {
        return id;
    }
    public void setId(Long id){
        this.id = id;
    }
    public String getReviewText() {
        return reviewText;
    }
    public void setReviewText(String reviewText) {
        this.reviewText = reviewText;
    }
    public int getRating() {
        return rating;
    }
    public void setRating(int rating) {
        this.rating = rating;
    }
}